"use client";
import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { apiClient } from "@/lib/api";
import { useAuth } from "@/hooks/useAuth";
import { tokenManager } from "@/lib/tokenManager";
import UnifiedChatComponent from "./unified-chat";
import { OtpModal } from "@/components/shared/otp-modal";
import { ErrorDialog } from "@/components/shared/error-dialog";
import { Loader } from "@/components/shared/loader";
import { ChatRoom, ApiResponse } from "@/types/chat";

interface PatientChatProps {
  roomIdentifier: string;
  initialToken?: string;
}

/**
 * Patient Chat Component
 * Handles patient-specific chat functionality including OTP verification
 * Uses the new unified architecture with cookie-based token management
 */
const PatientChatComponent: React.FC<PatientChatProps> = ({
  roomIdentifier,
  initialToken,
}) => {
  const [chatData, setChatData] = useState<ChatRoom | null>(null);
  const [isConsultOpen, setIsConsultOpen] = useState(true);
  const [showOtpModal, setShowOtpModal] = useState(false);
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [isLoading, setIsLoading] = useState(true);

  const { isAuthenticated, verifyOtp, sendOtp } = useAuth();
  const router = useRouter();

  // Initialize authentication and load chat data
  useEffect(() => {
    initializeChat();
  }, [roomIdentifier]);

  const initializeChat = async () => {
    setIsLoading(true);

    try {
      // If initial token is provided (from URL), set it
      if (initialToken) {
        await tokenManager.setToken(initialToken);
      }

      // Check if user is authenticated
      const token = await tokenManager.getTokenWithMigration();

      if (!token) {
        // No token found, show OTP modal
        setShowOtpModal(true);
        setIsLoading(false);
        return;
      }

      // Load chat data
      await loadChatData(token);
    } catch (error) {
      console.error("Failed to initialize chat:", error);
      handleChatError("Failed to initialize chat");
    } finally {
      setIsLoading(false);
    }
  };

  const loadChatData = async (token: string) => {
    try {
      const response: ApiResponse<ChatRoom> = await apiClient({
        method: "GET",
        endpoint: `chat?roomId=${roomIdentifier}`,
        token,
      });

      if (response.statusCode === 200 && response.data) {
        setChatData(response.data);

        // Check if consultation is active
        if (response.data.room.active) {
          setIsConsultOpen(true);
          toast.success("Chat room initiated.", {
            duration: 1000,
            closeButton: true,
          });
        } else {
          setIsConsultOpen(false);
          toast.info("Consultation is not currently active");
        }
      } else if (response.statusCode === 500) {
        // Token might be invalid, show OTP modal
        setShowOtpModal(true);
        await sendOtpToUser();
      } else {
        throw new Error(response.message || "Failed to load chat data");
      }
    } catch (error: any) {
      if (error?.response?.data?.statusCode === 400) {
        handleChatError(error.response.data.message || "Invalid chat room");
      } else if (error?.response?.data?.statusCode === 500) {
        // Clear invalid tokens and refresh
        await tokenManager.removeToken();
        router.refresh();
      } else {
        handleChatError("Something went wrong while loading chat");
      }
    }
  };

  const handleChatError = (message: string) => {
    setErrorMessage(message);
    setShowErrorDialog(true);
    toast.error(message, { duration: Infinity });
  };

  const sendOtpToUser = async () => {
    if (!chatData?.patient?.user_guid) {
      toast.error("Unable to send OTP - user information not available");
      return;
    }

    try {
      await sendOtp(chatData.patient.user_guid);
    } catch (error) {
      console.error("Failed to send OTP:", error);
      toast.error("Failed to send OTP");
    }
  };

  const handleOtpVerification = async (otp: string) => {
    if (!chatData?.patient?.user_guid) {
      toast.error("User information not available");
      return false;
    }

    const success = await verifyOtp(chatData.patient.user_guid, otp);

    if (success) {
      setShowOtpModal(false);
      // Reload chat data with new token
      const token = await tokenManager.getToken();
      if (token) {
        await loadChatData(token);
      }
    }

    return success;
  };

  const handleResendOtp = async () => {
    await sendOtpToUser();
  };

  // Show loading state
  if (isLoading) {
    return <Loader show={true} />;
  }

  // Show error dialog
  if (showErrorDialog) {
    return (
      <ErrorDialog
        isOpen={showErrorDialog}
        onClose={() => setShowErrorDialog(false)}
        title="Chat Error"
        message={errorMessage}
        onRetry={() => {
          setShowErrorDialog(false);
          initializeChat();
        }}
      />
    );
  }

  // Show OTP modal if authentication is required
  if (showOtpModal || !isAuthenticated) {
    return (
      <OtpModal
        isOpen={showOtpModal}
        onClose={() => setShowOtpModal(false)}
        onVerify={handleOtpVerification}
        onResendOtp={handleResendOtp}
        userEmail={chatData?.patient?.email}
      />
    );
  }

  // Show chat interface if everything is loaded
  if (!chatData || !isConsultOpen) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">
            {!chatData ? "Loading chat..." : "Consultation not active"}
          </h2>
          {!isConsultOpen && (
            <p className="text-gray-600">
              Please wait for the provider to start the consultation.
            </p>
          )}
        </div>
      </div>
    );
  }

  return (
    <UnifiedChatComponent
      patient={chatData.patient}
      provider={chatData.provider}
      room={chatData.room}
      role="PATIENT"
    />
  );
};

export default PatientChatComponent;
