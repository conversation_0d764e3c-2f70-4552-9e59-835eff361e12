"use client";
import React, { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "sonner";
import { apiClient } from "@/lib/api";
import { useAuth } from "@/hooks/useAuth";
import { tokenManager } from "@/lib/tokenManager";
import UnifiedChatComponent from "./unified-chat";
import { OtpModal } from "@/components/shared/otp-modal";
import { ErrorDialog } from "@/components/shared/error-dialog";
import { Loader } from "@/components/shared/loader";
import { ChatRoom, ApiResponse, UserRole, Patient, Provider, Room } from "@/types/chat";
import CONSTANT from "@/lib/constant";

interface UnifiedChatPageProps {
  roomIdentifier: string;
  role: UserRole;
  token?: string | null;
  email?: string;
  patient?: Patient;
  provider?: Provider;
  room?: Room;
  isConsult?: boolean;
}

/**
 * Unified Chat Page Component
 * Handles authentication and chat initialization for all user roles
 * Supports inline authentication with OTP for users without tokens
 */
const UnifiedChatPage: React.FC<UnifiedChatPageProps> = ({
  roomIdentifier,
  role,
  token: initialToken,
  email: urlEmail,
  patient: initialPatient,
  provider: initialProvider,
  room: initialRoom,
  isConsult = true,
}) => {
  const [chatData, setChatData] = useState<ChatRoom | null>(null);
  const [isConsultOpen, setIsConsultOpen] = useState(isConsult);
  const [showOtpModal, setShowOtpModal] = useState(false);
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [email, setEmail] = useState(urlEmail || "");

  const { isAuthenticated, verifyOtp, sendOtp } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Auto-fill email from URL params
  useEffect(() => {
    if (urlEmail) {
      setEmail(urlEmail);
    }
  }, [urlEmail]);

  // Initialize authentication and chat data
  useEffect(() => {
    const initializeChat = async () => {
      setIsLoading(true);
      
      try {
        // Check if we have a token
        const currentToken = initialToken || await tokenManager.getToken();
        
        if (currentToken) {
          // Try to load chat data with existing token
          await loadChatData(currentToken);
        } else if (role === CONSTANT.ROLES.USER) {
          // For users without token, show OTP modal
          setShowOtpModal(true);
          if (email) {
            await sendOtpToUser();
          }
        } else {
          // For providers/admins without token, redirect to login
          setErrorMessage("Authentication required. Please login to continue.");
          setShowErrorDialog(true);
        }
      } catch (error) {
        console.error("Failed to initialize chat:", error);
        setErrorMessage("Failed to initialize chat. Please try again.");
        setShowErrorDialog(true);
      } finally {
        setIsLoading(false);
      }
    };

    initializeChat();
  }, [roomIdentifier, initialToken, role, email]);

  const loadChatData = async (token: string) => {
    try {
      // If we already have patient/provider/room data from server, use it
      if (initialPatient && initialProvider && initialRoom) {
        setChatData({
          id: initialRoom.id,
          room: initialRoom,
          patient: initialPatient,
          provider: initialProvider,
          messages: [],
        });
        setIsConsultOpen(initialRoom.active);
        return;
      }

      // Otherwise, fetch chat data from API
      const response: ApiResponse<ChatRoom> = await apiClient({
        method: "GET",
        endpoint: `chat?roomId=${roomIdentifier}`,
        token,
      });

      if (response.statusCode === 200 && response.data) {
        setChatData(response.data);
        setIsConsultOpen(response.data.room.active);
        
        if (response.data.room.active) {
          toast.success("Chat room initiated.", {
            duration: 1000,
            closeButton: true,
          });
        } else {
          toast.info("Consultation is not currently active");
        }
      } else if (response.statusCode === 500) {
        // Token might be invalid, show OTP modal for users
        if (role === CONSTANT.ROLES.USER) {
          setShowOtpModal(true);
          await sendOtpToUser();
        } else {
          throw new Error("Authentication failed");
        }
      } else {
        throw new Error(response.message || "Failed to load chat data");
      }
    } catch (error) {
      console.error("Error loading chat data:", error);
      if (role === CONSTANT.ROLES.USER) {
        setShowOtpModal(true);
        await sendOtpToUser();
      } else {
        setErrorMessage("Failed to load chat data. Please try again.");
        setShowErrorDialog(true);
      }
    }
  };

  const sendOtpToUser = async () => {
    if (!email) {
      setErrorMessage("Email is required for authentication");
      setShowErrorDialog(true);
      return;
    }

    try {
      const success = await sendOtp(email);
      if (success) {
        toast.success("OTP sent to your email");
      } else {
        setErrorMessage("Failed to send OTP. Please try again.");
        setShowErrorDialog(true);
      }
    } catch (error) {
      console.error("Error sending OTP:", error);
      setErrorMessage("Failed to send OTP. Please try again.");
      setShowErrorDialog(true);
    }
  };

  const handleOtpVerification = async (otp: string) => {
    if (!email) {
      setErrorMessage("Email is required for verification");
      setShowErrorDialog(true);
      return;
    }

    try {
      const success = await verifyOtp(email, otp);
      if (success) {
        setShowOtpModal(false);
        const token = await tokenManager.getToken();
        if (token) {
          await loadChatData(token);
        }
      } else {
        setErrorMessage("Invalid OTP. Please try again.");
        setShowErrorDialog(true);
      }
    } catch (error) {
      console.error("Error verifying OTP:", error);
      setErrorMessage("Failed to verify OTP. Please try again.");
      setShowErrorDialog(true);
    }
  };

  const handleResendOtp = async () => {
    await sendOtpToUser();
  };

  const handleEmailChange = (newEmail: string) => {
    setEmail(newEmail);
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader />
      </div>
    );
  }

  // Show OTP modal for authentication
  if (showOtpModal) {
    return (
      <>
        <OtpModal
          isOpen={showOtpModal}
          onClose={() => setShowOtpModal(false)}
          onVerify={handleOtpVerification}
          onResend={handleResendOtp}
          email={email}
          onEmailChange={handleEmailChange}
        />
        <ErrorDialog
          isOpen={showErrorDialog}
          onClose={() => setShowErrorDialog(false)}
          message={errorMessage}
        />
      </>
    );
  }

  // Show error dialog
  if (showErrorDialog) {
    return (
      <ErrorDialog
        isOpen={showErrorDialog}
        onClose={() => setShowErrorDialog(false)}
        message={errorMessage}
      />
    );
  }

  // Show consultation status for users
  if (role === CONSTANT.ROLES.USER && (!chatData || !isConsultOpen)) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">
            {!chatData ? "Loading chat..." : "Consultation not active"}
          </h2>
          {!isConsultOpen && (
            <p className="text-gray-600">
              Please wait for the provider to start the consultation.
            </p>
          )}
        </div>
      </div>
    );
  }

  // Show chat interface
  if (!chatData) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Loading chat...</h2>
        </div>
      </div>
    );
  }

  return (
    <UnifiedChatComponent
      patient={chatData.patient}
      provider={chatData.provider}
      room={chatData.room}
      role={role}
    />
  );
};

export default UnifiedChatPage;
