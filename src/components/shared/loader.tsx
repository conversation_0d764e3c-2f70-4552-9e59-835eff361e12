import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { But<PERSON> } from "@/components/ui/button"
import { LoaderCircle } from 'lucide-react'

export function Loader({
  show
}: {
  show: boolean
}) {
  return (
    <AlertDialog open={show}>
      <AlertDialogContent className='w-[120px] h-[120px] display-flex justify-center items-center'>
        <LoaderCircle className='animate-spin text-primary h-[80px] w-[80px]'/>       
      </AlertDialogContent>
    </AlertDialog>
  )
}
