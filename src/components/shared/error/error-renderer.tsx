import { notFound } from 'next/navigation';
import React from 'react';
import SessionExpired from './session-expired';
import GenericError from './generic-error';
import ChatRoomNotFound from './not-found';

interface ErrorRenderedProps {
  statusCode: number;
  children?: React.ReactNode;
}

const ErrorRendered = ({ statusCode, children }: ErrorRenderedProps) => {

  if (statusCode === 404) {
    return <ChatRoomNotFound/>
  }

  if (statusCode === 500) {
    return <div>{children}</div>;
  }

  if (statusCode === 401) {
    return <SessionExpired/>;
  }

  if (statusCode === 403) {
    return <SessionExpired/>;
  }

  // if (statusCode === 400) {
  //   return notFound();
  // }
  
  return <GenericError/>;
};

export default ErrorRendered;
