import React from 'react';

const GenericError = () => {
  return (
    <div className='flex min-h-[100dvh] flex-col items-center justify-center bg-background px-4 py-12 sm:px-6 lg:px-8'>
      <div className='mx-auto max-w-md text-center'>
        <div className='mx-auto h-12 w-12 text-primary' />
        <h1 className='mt-4 text-3xl font-bold tracking-tight text-foreground sm:text-4xl'>Something went wrong!</h1>
        <p className='mt-4 text-muted-foreground'>Please Refresh and Try again!!</p>
      </div>
    </div>
  );
};

export default GenericError;
