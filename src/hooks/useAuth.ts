"use client";
import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { apiClient } from '@/lib/api';
import { tokenManager } from '@/lib/tokenManager';
import { User, UseAuth, OtpVerificationRequest, AuthResponse, ApiResponse } from '@/types/chat';

/**
 * Custom hook for authentication management
 * Handles login, logout, OTP verification, and token management
 */
export const useAuth = (): UseAuth => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      setIsLoading(true);
      try {
        // Migrate legacy tokens if needed
        await tokenManager.migrateLegacyTokens();
        
        const authToken = await tokenManager.getToken();
        const authUser = await tokenManager.getUser();
        
        setToken(authToken);
        setUser(authUser);
      } catch (err) {
        console.error('Failed to initialize auth:', err);
        setError('Failed to initialize authentication');
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = useCallback(async (authToken: string, authUser: User): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    try {
      await tokenManager.setToken(authToken, authUser);
      setToken(authToken);
      setUser(authUser);
      toast.success('Successfully logged in');
    } catch (err) {
      const errorMessage = 'Failed to save authentication data';
      setError(errorMessage);
      toast.error(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const logout = useCallback(async (): Promise<void> => {
    setIsLoading(true);
    
    try {
      await tokenManager.removeToken();
      setToken(null);
      setUser(null);
      setError(null);
      toast.success('Successfully logged out');
      router.refresh();
    } catch (err) {
      console.error('Failed to logout:', err);
      toast.error('Failed to logout properly');
    } finally {
      setIsLoading(false);
    }
  }, [router]);

  const verifyOtp = useCallback(async (userId: string, otp: string): Promise<boolean> => {
    if (!otp.trim()) {
      setError('Please enter OTP');
      toast.error('Please enter OTP');
      return false;
    }

    setIsLoading(true);
    setError(null);

    try {
      const requestData: OtpVerificationRequest = {
        user_id: userId,
        otp: otp.trim(),
      };

      const response: ApiResponse<AuthResponse> = await apiClient({
        method: 'POST',
        endpoint: 'auth/verify-otp',
        data: requestData,
      });

      if (response.statusCode === 200 && response.data) {
        await login(response.data.token, response.data.user);
        toast.success(response.message || 'OTP verified successfully');
        return true;
      } else {
        const errorMessage = response.message || 'OTP verification failed';
        setError(errorMessage);
        toast.error(errorMessage);
        return false;
      }
    } catch (err) {
      const errorMessage = 'Failed to verify OTP';
      setError(errorMessage);
      toast.error(errorMessage);
      console.error('OTP verification error:', err);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [login]);

  const sendOtp = useCallback(async (userId: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      const response: ApiResponse = await apiClient({
        method: 'POST',
        endpoint: 'auth/send-otp',
        data: { user_id: userId },
      });

      if (response.statusCode === 200) {
        toast.success(response.message || 'OTP sent successfully');
        return true;
      } else {
        const errorMessage = response.message || 'Failed to send OTP';
        setError(errorMessage);
        toast.error(errorMessage);
        return false;
      }
    } catch (err) {
      const errorMessage = 'Failed to send OTP';
      setError(errorMessage);
      toast.error(errorMessage);
      console.error('Send OTP error:', err);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshToken = useCallback(async (): Promise<boolean> => {
    try {
      const currentToken = await tokenManager.getToken();
      if (!currentToken) return false;

      // You can implement token refresh logic here if your API supports it
      // For now, we'll just validate the current token
      return !!currentToken;
    } catch (err) {
      console.error('Token refresh failed:', err);
      return false;
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    user,
    token,
    isAuthenticated: !!token && !!user,
    login,
    logout,
    verifyOtp,
    sendOtp,
    refreshToken,
    isLoading,
    error,
    clearError,
  };
};

export default useAuth;
