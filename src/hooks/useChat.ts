"use client";
import { useState, useEffect, useCallback, useContext } from 'react';
import { toast } from 'sonner';
import { apiClient } from '@/lib/api';
import { tokenManager } from '@/lib/tokenManager';
import { SocketContext } from '@/context/socket';
import { 
  ChatMessage, 
  UseChat, 
  FileUploadResponse, 
  SignUrlResponse, 
  ApiResponse,
  CHAT_EVENTS 
} from '@/types/chat';
import { validateFile } from '@/lib/utils';

/**
 * Custom hook for chat functionality
 * Handles message sending, file uploads, and real-time communication
 */
export const useChat = (roomId: string, userId: string): UseChat => {
  const [messageHistory, setMessageHistory] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { socket, isConnected } = useContext(SocketContext);

  // Initialize chat and join room
  useEffect(() => {
    if (socket && roomId && userId) {
      socket.emit(CHAT_EVENTS.JOIN_ROOM, {
        room_id: roomId,
        user_id: userId,
      });
    }

    return () => {
      if (socket && roomId) {
        socket.emit(CHAT_EVENTS.LEAVE_ROOM, { room_id: roomId });
      }
    };
  }, [socket, roomId, userId]);

  // Listen for incoming messages
  useEffect(() => {
    if (!socket) return;

    const handleReceiveMessage = (data: ChatMessage) => {
      setMessageHistory(prev => [...prev, data]);
    };

    socket.on(CHAT_EVENTS.RECEIVE_MESSAGE, handleReceiveMessage);

    return () => {
      socket.off(CHAT_EVENTS.RECEIVE_MESSAGE, handleReceiveMessage);
    };
  }, [socket]);

  const sendMessage = useCallback(async (message: string): Promise<void> => {
    if (!message.trim()) {
      toast.error('Please enter a message');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const token = await tokenManager.getToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      const messageData = {
        message: message.trim(),
        room_id: roomId,
        sender_id: userId,
      };

      // Send via socket for real-time delivery
      if (socket && isConnected) {
        socket.emit(CHAT_EVENTS.SEND_MESSAGE, messageData);
      }

      // Also send via API for persistence
      const response: ApiResponse = await apiClient({
        method: 'POST',
        endpoint: 'chat/send',
        data: messageData,
        token,
      });

      if (response.statusCode !== 200) {
        throw new Error(response.message || 'Failed to send message');
      }

    } catch (err: any) {
      const errorMessage = err.message || 'Failed to send message';
      setError(errorMessage);
      toast.error(errorMessage);
      console.error('Send message error:', err);
    } finally {
      setIsLoading(false);
    }
  }, [roomId, userId, socket, isConnected]);

  const uploadFile = useCallback(async (file: File): Promise<void> => {
    // Validate file
    const validation = validateFile(file);
    if (!validation.isValid) {
      toast.error(validation.error);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const token = await tokenManager.getToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      const formData = new FormData();
      formData.append('file', file);
      formData.append('room_id', roomId);
      formData.append('sender_id', userId);

      const response: ApiResponse<FileUploadResponse> = await apiClient({
        method: 'POST',
        endpoint: 'files/upload',
        data: formData,
        token,
        isFormData: true,
      });

      if (response.statusCode === 200 && response.data) {
        // Send file message via socket
        const fileMessage = {
          message: 'ATTACHMENT',
          room_id: roomId,
          sender_id: userId,
          file: {
            path: response.data.file_id,
            name: file.name,
            size: file.size,
            type: file.type,
          },
        };

        if (socket && isConnected) {
          socket.emit(CHAT_EVENTS.SEND_MESSAGE, fileMessage);
        }

        toast.success('File uploaded successfully');
      } else {
        throw new Error(response.message || 'Failed to upload file');
      }

    } catch (err: any) {
      const errorMessage = err.message || 'Failed to upload file';
      setError(errorMessage);
      toast.error(errorMessage);
      console.error('File upload error:', err);
    } finally {
      setIsLoading(false);
    }
  }, [roomId, userId, socket, isConnected]);

  const getFileSignUrl = useCallback(async (fileId: string): Promise<string | null> => {
    setIsLoading(true);
    setError(null);

    try {
      const token = await tokenManager.getToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      const response: ApiResponse<SignUrlResponse> = await apiClient({
        method: 'POST',
        endpoint: 'files/sign-url',
        data: { file_id: fileId },
        token,
      });

      if (response.statusCode === 200 && response.data) {
        return response.data.url;
      } else {
        throw new Error(response.message || 'Failed to get file URL');
      }

    } catch (err: any) {
      const errorMessage = err.message || 'Failed to get file URL';
      setError(errorMessage);
      toast.error(errorMessage);
      console.error('Get file URL error:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const loadMessageHistory = useCallback(async (): Promise<void> => {
    setIsLoading(true);
    setError(null);

    try {
      const token = await tokenManager.getToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      const response: ApiResponse<{ messages: ChatMessage[] }> = await apiClient({
        method: 'GET',
        endpoint: `chat?roomId=${roomId}`,
        token,
      });

      if (response.statusCode === 200 && response.data) {
        setMessageHistory(response.data.messages || []);
      } else {
        throw new Error(response.message || 'Failed to load messages');
      }

    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load messages';
      setError(errorMessage);
      console.error('Load messages error:', err);
    } finally {
      setIsLoading(false);
    }
  }, [roomId]);

  const markMessagesAsRead = useCallback(async (): Promise<void> => {
    try {
      const token = await tokenManager.getToken();
      if (!token) return;

      await apiClient({
        method: 'POST',
        endpoint: 'chat/mark-read',
        data: { room_id: roomId, user_id: userId },
        token,
      });
    } catch (err) {
      console.error('Failed to mark messages as read:', err);
    }
  }, [roomId, userId]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    messageHistory,
    sendMessage,
    uploadFile,
    getFileSignUrl,
    loadMessageHistory,
    markMessagesAsRead,
    isConnected,
    isLoading,
    error,
    clearError,
  };
};

export default useChat;
