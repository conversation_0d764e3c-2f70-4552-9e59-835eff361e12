import { useState, useEffect } from 'react';
interface NotificationOptions {
  title: string;
  body: string;
}

const defaultSound = '/audio/notification.wav';

const useNotification = () => {
  const [permission, setPermission] = useState(Notification.permission);
  const [notificationInstance, setNotificationInstance] = useState<Notification | null>(null);

  useEffect(() => {
    const preloadAudio = () => {
      const audio = new Audio(defaultSound);
      audio.preload = 'auto';
    };

    preloadAudio();
  }, []);

  useEffect(() => {
    if (permission !== 'granted' && permission !== 'denied') {
      Notification.requestPermission().then((perm) => {
        setPermission(perm);
      });
    }
  }, [permission]);

  const playSound = (soundUrl: string) => {
    const audio = new Audio(soundUrl);
    audio.play().catch((error) => {
    console.error('Error playing notification sound:', error);
    });
    
  };

  const sendNotification = (payload: NotificationOptions) => {
    if (permission === 'granted') {
      // Close the existing notification if there's already one
      if (notificationInstance) {
        notificationInstance.close();
      }

      // Create a new notification
      const notification = new Notification(payload.title, {
        body: payload.body,
        icon: '/images/logo.png',
      });

      // Set the new notification instance
      setNotificationInstance(notification);

      const soundUrl = defaultSound;
      playSound(soundUrl);

      // Clear the notification instance when it's closed
      notification.onclose = () => {
        setNotificationInstance(null);
      };
      notification.onclick = () => {
        notification.close();
        window.parent.focus();
      };

      return notification;
    } else if (permission === 'default') {
      Notification.requestPermission().then((perm) => {
        if (perm === 'granted') {
          sendNotification({
            title: payload.title,
            body: payload.body,
          });
        }
      });
    } else {
      console.warn('Notifications are blocked. Please allow them in your browser settings.');
    }
  };

  return sendNotification;
};

export default useNotification;
