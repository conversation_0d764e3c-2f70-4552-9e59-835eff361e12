'use client';
import React, { useEffect } from 'react';
import { toast } from 'sonner';

const NotificationProvider = ({ children }: { children: React.ReactNode }) => {
  useEffect(() => {
    const permission = Notification.permission;
    if (permission === 'default') {
      Notification.requestPermission(function (permission) {
        if (permission === 'granted') {
          toast.success('Notification permission granted', {
            duration: 1000,
          });
        }
      });
    }
  }, []);

  return <>{children}</>;
};

export default NotificationProvider;
