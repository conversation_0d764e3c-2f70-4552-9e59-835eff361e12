'use client';
import React, { useEffect, useState, useCallback, useRef } from 'react';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Button, buttonVariants } from '@/components/ui/button';


const InactivityPopup = () => {
  const [showPopup, setShowPopup] = useState<boolean>(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const timeoutMinutes = 20 

  // Function to reset the inactivity timer
  const resetTimer = useCallback(() => {
    if (timerRef.current) clearTimeout(timerRef.current);
    timerRef.current = setTimeout(() => {
      setShowPopup(true);
    }, timeoutMinutes * 60 * 1000);
  }, [timeoutMinutes]);

  // Debounced function to handle user activity
  const debouncedHandleUserActivity = useCallback(() => {
    if (!showPopup) {
      resetTimer();
    }
  }, [resetTimer, showPopup]);

  useEffect(() => {
    // Function to debounce rapid event calls
    const handleUserActivity = () => {
      if (timerRef.current) clearTimeout(timerRef.current);
      debouncedHandleUserActivity();
    };

    // Add event listeners for user activity with passive option where applicable
    const options = { passive: true };
    window.addEventListener('mousemove', handleUserActivity, options);
    window.addEventListener('keypress', handleUserActivity);
    window.addEventListener('touchstart', handleUserActivity, options);

    // Start the inactivity timer
    resetTimer();

    // Cleanup event listeners and timer on component unmount
    return () => {
      if (timerRef.current) clearTimeout(timerRef.current);
      window.removeEventListener('mousemove', handleUserActivity);
      window.removeEventListener('keypress', handleUserActivity);
      window.removeEventListener('touchstart', handleUserActivity);
    };
  }, [debouncedHandleUserActivity, resetTimer]);

  const handleCloseTab = () => {
    // if (onTabClose) onTabClose(); // Call custom callback if provided
    // window.close();
    setShowPopup(false);
  };

  return (
    <>
      <AlertDialog open={showPopup}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Inactivity Alert: Please Close This Tab</AlertDialogTitle>
            <AlertDialogDescription>You have been inactive for 20 minutes. Please close this tab if you are not using it.</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleCloseTab} className={buttonVariants({ variant: 'default' })}>
              Okay
            </AlertDialogCancel>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default InactivityPopup;
