import { User<PERSON>ole } from "@/types/chat";
import CONSTANT from "./constant";
import { tokenManager } from "./tokenManager";

/**
 * Utility functions for role detection and management
 */

/**
 * Decode JWT token to extract role information
 * @param token JWT token string
 * @returns Decoded token payload or null if invalid
 */
export const decodeToken = (token: string): any | null => {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Error decoding token:', error);
    return null;
  }
};

/**
 * Extract user role from token
 * @param token JWT token string
 * @returns UserRole or null if not found
 */
export const getRoleFromToken = (token: string): UserRole | null => {
  const decoded = decodeToken(token);
  if (!decoded) return null;

  // Check various possible role fields in the token
  const role = decoded.role || decoded.user_role || decoded.userRole || decoded.type;
  
  if (role && Object.values(CONSTANT.ROLES).includes(role)) {
    return role as UserRole;
  }

  return null;
};

/**
 * Determine user role from multiple sources
 * Priority: URL param > Token > Cookie > Default
 * @param urlRole Role from URL parameters
 * @param token JWT token
 * @returns UserRole
 */
export const determineUserRole = async (
  urlRole?: string,
  token?: string | null
): Promise<UserRole> => {
  // 1. Check URL parameter first
  if (urlRole && Object.values(CONSTANT.ROLES).includes(urlRole as UserRole)) {
    return urlRole as UserRole;
  }

  // 2. Check token for role information
  if (token) {
    const tokenRole = getRoleFromToken(token);
    if (tokenRole) {
      return tokenRole;
    }
  }

  // 3. Check stored token
  try {
    const storedToken = await tokenManager.getToken();
    if (storedToken) {
      const tokenRole = getRoleFromToken(storedToken);
      if (tokenRole) {
        return tokenRole;
      }
    }
  } catch (error) {
    console.error('Error getting stored token:', error);
  }

  // 4. Default to USER role
  return CONSTANT.DEFAULT_ROLE as UserRole;
};

/**
 * Check if user has permission for a specific action based on role
 * @param userRole Current user role
 * @param requiredRole Minimum required role
 * @returns boolean indicating if user has permission
 */
export const hasPermission = (userRole: UserRole, requiredRole: UserRole): boolean => {
  const roleHierarchy = {
    [CONSTANT.ROLES.USER]: 1,
    [CONSTANT.ROLES.PROVIDER]: 2,
    [CONSTANT.ROLES.ADMIN]: 3,
  };

  return roleHierarchy[userRole] >= roleHierarchy[requiredRole];
};

/**
 * Get role-specific features/capabilities
 * @param role User role
 * @returns Object with role-specific features
 */
export const getRoleFeatures = (role: UserRole) => {
  const features = {
    canToggleConsultation: false,
    canViewAllRooms: false,
    canManageUsers: false,
    canAccessAdminPanel: false,
    showProviderControls: false,
  };

  switch (role) {
    case CONSTANT.ROLES.ADMIN:
      return {
        ...features,
        canToggleConsultation: true,
        canViewAllRooms: true,
        canManageUsers: true,
        canAccessAdminPanel: true,
        showProviderControls: true,
      };
    case CONSTANT.ROLES.PROVIDER:
      return {
        ...features,
        canToggleConsultation: true,
        showProviderControls: true,
      };
    case CONSTANT.ROLES.USER:
    default:
      return features;
  }
};

/**
 * Validate if a role transition is allowed
 * @param currentRole Current user role
 * @param targetRole Target role to switch to
 * @returns boolean indicating if transition is allowed
 */
export const canSwitchRole = (currentRole: UserRole, targetRole: UserRole): boolean => {
  // Only admins can switch to any role
  if (currentRole === CONSTANT.ROLES.ADMIN) {
    return true;
  }

  // Providers can switch to user role
  if (currentRole === CONSTANT.ROLES.PROVIDER && targetRole === CONSTANT.ROLES.USER) {
    return true;
  }

  // Users cannot switch roles
  return false;
};

export default {
  decodeToken,
  getRoleFromToken,
  determineUserRole,
  hasPermission,
  getRoleFeatures,
  canSwitchRole,
};
