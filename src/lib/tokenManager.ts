"use client";
import { get<PERSON><PERSON><PERSON>, set<PERSON><PERSON><PERSON>, remove<PERSON><PERSON><PERSON> } from '@/actions/cookies';
import CONSTANT from './constant';

/**
 * Unified token management utility
 * Handles both client-side and server-side token operations
 * Migrates from localStorage to cookies for better security and SSR compatibility
 */

interface TokenData {
  token: string;
  user?: any;
  expiresAt?: Date;
}

class TokenManager {
  private static instance: TokenManager;
  private tokenCache: Map<string, TokenData> = new Map();

  private constructor() {}

  static getInstance(): TokenManager {
    if (!TokenManager.instance) {
      TokenManager.instance = new TokenManager();
    }
    return TokenManager.instance;
  }

  /**
   * Set token in cookies (client-side)
   */
  async setToken(token: string, user?: any): Promise<void> {
    try {
      await setCookie(CONSTANT.ACCESS_TOKEN, token);
      
      if (user) {
        await setCookie('user', JSON.stringify(user));
      }

      // Cache the token for immediate access
      this.tokenCache.set(CONSTANT.ACCESS_TOKEN, {
        token,
        user,
        expiresAt: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours
      });

      // Clear any existing localStorage tokens for migration
      this.clearLegacyTokens();
    } catch (error) {
      console.error('Failed to set token:', error);
      throw new Error('Failed to save authentication token');
    }
  }

  /**
   * Get token from cookies
   */
  async getToken(): Promise<string | null> {
    try {
      // First check cache
      const cached = this.tokenCache.get(CONSTANT.ACCESS_TOKEN);
      if (cached && cached.expiresAt && cached.expiresAt > new Date()) {
        return cached.token;
      }

      // Get from cookies
      const token = await getCookie(CONSTANT.ACCESS_TOKEN);
      
      if (token) {
        // Update cache
        this.tokenCache.set(CONSTANT.ACCESS_TOKEN, {
          token,
          expiresAt: new Date(Date.now() + 2 * 60 * 60 * 1000),
        });
      }

      return token || null;
    } catch (error) {
      console.error('Failed to get token:', error);
      return null;
    }
  }

  /**
   * Get user data from cookies
   */
  async getUser(): Promise<any | null> {
    try {
      // Check cache first
      const cached = this.tokenCache.get(CONSTANT.ACCESS_TOKEN);
      if (cached?.user) {
        return cached.user;
      }

      const userStr = await getCookie('user');
      if (userStr) {
        try {
          const user = JSON.parse(userStr);
          // Update cache
          const tokenData = this.tokenCache.get(CONSTANT.ACCESS_TOKEN) || { token: '' };
          this.tokenCache.set(CONSTANT.ACCESS_TOKEN, { ...tokenData, user });
          return user;
        } catch (parseError) {
          console.error('Failed to parse user data:', parseError);
        }
      }
      return null;
    } catch (error) {
      console.error('Failed to get user:', error);
      return null;
    }
  }

  /**
   * Remove token and user data
   */
  async removeToken(): Promise<void> {
    try {
      await removeCookie(CONSTANT.ACCESS_TOKEN);
      await removeCookie('user');
      this.tokenCache.clear();
      this.clearLegacyTokens();
    } catch (error) {
      console.error('Failed to remove token:', error);
    }
  }

  /**
   * Check if user is authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    const token = await this.getToken();
    return !!token;
  }

  /**
   * Clear legacy localStorage tokens for migration
   */
  private clearLegacyTokens(): void {
    if (typeof window !== 'undefined') {
      try {
        localStorage.removeItem('accessToken');
        localStorage.removeItem('user');
      } catch (error) {
        // Ignore localStorage errors
      }
    }
  }

  /**
   * Migrate from localStorage to cookies
   */
  async migrateLegacyTokens(): Promise<void> {
    if (typeof window === 'undefined') return;

    try {
      const legacyToken = localStorage.getItem('accessToken');
      const legacyUser = localStorage.getItem('user');

      if (legacyToken) {
        let user = null;
        if (legacyUser) {
          try {
            user = JSON.parse(legacyUser);
          } catch (error) {
            console.warn('Failed to parse legacy user data');
          }
        }

        await this.setToken(legacyToken, user);
        console.log('Successfully migrated tokens from localStorage to cookies');
      }
    } catch (error) {
      console.error('Failed to migrate legacy tokens:', error);
    }
  }

  /**
   * Get token with automatic migration fallback
   */
  async getTokenWithMigration(): Promise<string | null> {
    let token = await this.getToken();
    
    if (!token && typeof window !== 'undefined') {
      // Try to migrate from localStorage
      const legacyToken = localStorage.getItem('accessToken');
      if (legacyToken) {
        await this.migrateLegacyTokens();
        token = await this.getToken();
      }
    }

    return token;
  }
}

// Export singleton instance
export const tokenManager = TokenManager.getInstance();

// Export convenience functions
export const setAuthToken = (token: string, user?: any) => tokenManager.setToken(token, user);
export const getAuthToken = () => tokenManager.getTokenWithMigration();
export const removeAuthToken = () => tokenManager.removeToken();
export const isAuthenticated = () => tokenManager.isAuthenticated();
export const getAuthUser = () => tokenManager.getUser();
export const migrateLegacyTokens = () => tokenManager.migrateLegacyTokens();
