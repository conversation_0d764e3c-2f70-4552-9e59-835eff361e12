/**
 * Tests for role utility functions
 */
import { determineUserRole, getRoleFromToken, hasPermission, getRoleFeatures } from '../roleUtils';
import CONSTANT from '../constant';

// Mock tokenManager
jest.mock('../tokenManager', () => ({
  tokenManager: {
    getToken: jest.fn(),
  },
}));

describe('Role Utils', () => {
  describe('getRoleFromToken', () => {
    it('should extract role from valid token', () => {
      // Create a mock JWT token with role
      const payload = { role: 'PROVIDER', user_id: '123' };
      const base64Payload = btoa(JSON.stringify(payload));
      const mockToken = `header.${base64Payload}.signature`;

      const role = getRoleFromToken(mockToken);
      expect(role).toBe('PROVIDER');
    });

    it('should return null for invalid token', () => {
      const role = getRoleFromToken('invalid-token');
      expect(role).toBeNull();
    });
  });

  describe('determineUserRole', () => {
    it('should prioritize URL role parameter', async () => {
      const role = await determineUserRole('ADMIN', 'some-token');
      expect(role).toBe('ADMIN');
    });

    it('should fall back to default role when no valid role found', async () => {
      const role = await determineUserRole(undefined, undefined);
      expect(role).toBe(CONSTANT.DEFAULT_ROLE);
    });
  });

  describe('hasPermission', () => {
    it('should allow admin to access all roles', () => {
      expect(hasPermission('ADMIN', 'USER')).toBe(true);
      expect(hasPermission('ADMIN', 'PROVIDER')).toBe(true);
      expect(hasPermission('ADMIN', 'ADMIN')).toBe(true);
    });

    it('should allow provider to access user role', () => {
      expect(hasPermission('PROVIDER', 'USER')).toBe(true);
      expect(hasPermission('PROVIDER', 'PROVIDER')).toBe(true);
    });

    it('should not allow user to access higher roles', () => {
      expect(hasPermission('USER', 'PROVIDER')).toBe(false);
      expect(hasPermission('USER', 'ADMIN')).toBe(false);
    });
  });

  describe('getRoleFeatures', () => {
    it('should return correct features for admin', () => {
      const features = getRoleFeatures('ADMIN');
      expect(features.canToggleConsultation).toBe(true);
      expect(features.canViewAllRooms).toBe(true);
      expect(features.canManageUsers).toBe(true);
      expect(features.canAccessAdminPanel).toBe(true);
      expect(features.showProviderControls).toBe(true);
    });

    it('should return correct features for provider', () => {
      const features = getRoleFeatures('PROVIDER');
      expect(features.canToggleConsultation).toBe(true);
      expect(features.canViewAllRooms).toBe(false);
      expect(features.canManageUsers).toBe(false);
      expect(features.canAccessAdminPanel).toBe(false);
      expect(features.showProviderControls).toBe(true);
    });

    it('should return correct features for user', () => {
      const features = getRoleFeatures('USER');
      expect(features.canToggleConsultation).toBe(false);
      expect(features.canViewAllRooms).toBe(false);
      expect(features.canManageUsers).toBe(false);
      expect(features.canAccessAdminPanel).toBe(false);
      expect(features.showProviderControls).toBe(false);
    });
  });
});
