import axios, { AxiosRequestConfig } from "axios";

const apilink = process.env.NEXT_PUBLIC_BASE_URL;

interface ServerApiOptions {
  method: string;
  endpoint: string;
  data?: any;
  token?: string;
  timeout?: number;
}

/**
 * Server-side API client for making HTTP requests
 * Optimized for server-side rendering and API routes
 */
export const apiServer = async ({
  method,
  endpoint,
  data,
  token,
  timeout = 50000,
}: ServerApiOptions) => {
  const baseURL = `${apilink}/api/v1/`;

  const config: AxiosRequestConfig = {
    baseURL,
    method,
    url: endpoint,
    timeout,
    headers: {
      "Content-Type": "application/json",
      ...(token ? { Authorization: `Bearer ${token}` } : {}),
    },
  };

  // Add data for POST/PUT requests
  if (data && (method === "POST" || method === "PUT")) {
    config.data = data;
  }

  try {
    const response = await axios.request(config);
    return response.data;
  } catch (error: any) {
    return (
      error?.response?.data || { statusCode: 500, message: "Server error" }
    );
  }
};

/**
 * Legacy wrapper for backward compatibility
 * @deprecated Use apiServer with object parameter instead
 */
export const apiServerLegacy = async (
  method: string,
  endpoint: string,
  data: any,
  token?: string,
) => {
  return apiServer({ method, endpoint, data, token });
};

// Export legacy function as default export for backward compatibility
export default apiServerLegacy;
