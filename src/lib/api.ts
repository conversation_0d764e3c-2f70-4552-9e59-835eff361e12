import axios, { AxiosRequestConfig } from "axios";

const apiLink = process.env.NEXT_PUBLIC_BASE_URL;

interface ApiRequestOptions {
  method: string;
  endpoint: string;
  data?: any;
  token?: string;
  isFormData?: boolean;
  timeout?: number;
}

/**
 * Unified API client for making HTTP requests
 * Supports both JSON and FormData requests
 */
export const apiClient = async ({
  method,
  endpoint,
  data,
  token,
  isFormData = false,
  timeout = 50000,
}: ApiRequestOptions) => {
  const baseURL = `${apiLink ?? ""}/api/v1`;

  const config: AxiosRequestConfig = {
    baseURL,
    method,
    url: endpoint,
    timeout,
  };

  // Add data for POST/PUT requests
  if (
    data &&
    (method === "POST" || method === "PUT" || Object.keys(data).length > 0)
  ) {
    config.data = data;
  }

  // Add headers if token is provided
  if (token) {
    config.headers = {
      Authorization: `Bearer ${token}`,
      ...(isFormData ? { "Content-Type": "multipart/form-data" } : {}),
    };
  }

  try {
    const response = await axios.request(config);
    return response.data;
  } catch (error: any) {
    // Return error response data for consistent error handling
    if (error.response?.data) {
      return error.response.data;
    }
    throw error;
  }
};

/**
 * Legacy wrapper for makeRequest - for backward compatibility
 * @deprecated Use apiClient instead
 */
export const makeRequest = async (
  method: string,
  endpoint: string,
  data: any,
  token?: string,
) => {
  return apiClient({ method, endpoint, data, token });
};

/**
 * Legacy wrapper for makeRequestFormData - for backward compatibility
 * @deprecated Use apiClient with isFormData: true instead
 */
export const makeRequestFormData = async (
  method: string,
  endpoint: string,
  data: any,
  token?: string,
) => {
  return apiClient({ method, endpoint, data, token, isFormData: true });
};
