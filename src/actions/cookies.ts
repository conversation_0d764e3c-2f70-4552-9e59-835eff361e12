"use server";
import { cookies } from "next/headers";

/**
 * The setCookie function in TypeScript sets a cookie with specified attributes like name, value,
 * expiration time, and security settings.
 * @param {string} name - The `name` parameter in the `setCookie` function represents the name of the
 * cookie that you want to set. It is a string value that will be used to identify the cookie when
 * retrieving or updating its value.
 * @param {string} value - The `value` parameter in the `setCookie` function represents the value that
 * you want to store in the cookie. It is the data that you want to associate with the specified `name`
 * in the cookie.
 */
export const setCookie = async (name: string, value: string) => {
  cookies().set({
    name: name,
    value: value,
    httpOnly: true,
    path: "/",
    expires: new Date(Date.now() + 1000 * 60 * 60 * 2 * 1),
    sameSite: "lax",
    secure: process.env.NODE_ENV === "production",
  });
};

/**
 * The function `removeCookie` is used to delete a cookie by setting its value to an empty string and
 * expiring it.
 * @param {string} name - The `name` parameter is a string that represents the name of the cookie that
 * you want to remove from the browser.
 */
export const removeCookie = async (name: string) => {
  cookies().set({
    name: name,
    value: "",
    httpOnly: true,
    path: "/",
    expires: new Date(0),
    sameSite: "lax",
    secure: process.env.NODE_ENV === "production",
  });
};

/**
 * The function `getCookie` retrieves the value of a cookie by its name.
 * @param {string} name - The `name` parameter in the `getCookie` function is a string that represents
 * the name of the cookie you want to retrieve.
 * @returns The `getCookie` function is returning the value of the cookie with the specified name. If
 * the cookie exists, it will return the value of the cookie; otherwise, it will return `undefined`.
 */
export const getCookie = async (name: string) => {
  return cookies().get(name)?.value;
};