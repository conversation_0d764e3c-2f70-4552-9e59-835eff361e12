import ProviderChatUIComponent from "@/components/chat-ui/provider-chat";
import { cookies } from "next/headers";
import React from "react";
export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";
const ProvideChatUIPage = () => {
  const cookieStore = cookies();
  const token = cookieStore.get("accessToken")!;
  return (
    <>
      <ProviderChatUIComponent token={token?.value} />
    </>
  );
};

export default ProvideChatUIPage;
