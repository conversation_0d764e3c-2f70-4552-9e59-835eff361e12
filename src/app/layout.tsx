import type { Metada<PERSON> } from "next";
import { Inter as FontSans } from "next/font/google";
import "./globals.css";
import { cn } from "@/lib/utils";
import { Toaster } from "sonner";
import SocketProvider from "@/context/socket";
import NotificationProvider from "@/provider/notification-provider";
import InactivityPopupProvider from "@/provider/inactivity-popup-provider";

const fontSans = FontSans({
  subsets: ["latin"],
  variable: "--font-sans",
});

export const metadata: Metadata = {
  title: "Chat: Ola Digital Health",
  description: "Chat: Ola Digital Health",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={cn(
          "min-h-screen bg-background font-sans antialiased",
          fontSans.variable,
        )}
      >
        <NotificationProvider>
          <SocketProvider>
            <InactivityPopupProvider />
            <Toaster position="top-center" />
            {children}
          </SocketProvider>
        </NotificationProvider>
      </body>
    </html>
  );
}
