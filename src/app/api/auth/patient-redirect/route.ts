import { setCookie } from "@/actions/cookies";
import { redirect, RedirectType } from "next/navigation";
import CONSTANT from "@/lib/constant";

/**
 * Redirects the provider to the chat provider page after authentication.
 *
 * @param {Request} request - The incoming request object.
 * @return {NextResponse} The redirect response to the chat provider page.
 */
const providerAuthRedirect = async (request: Request) => {
  const requestUrl = new URL(request.url);

  const roomId = requestUrl.searchParams.get("room");

  const token = requestUrl.searchParams.get("token");

  await setCookie(CONSTANT.ACCESS_TOKEN, token as string);

  return redirect(`/chat/patient/${roomId}`, RedirectType.replace);
};

export { providerAuthRedirect as GET };
