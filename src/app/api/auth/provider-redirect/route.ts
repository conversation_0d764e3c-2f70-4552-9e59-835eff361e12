import { setCookie } from "@/actions/cookies";
import { redirect, RedirectType } from "next/navigation";
import CONSTANT from "@/lib/constant";

/**
 * Redirects the provider to the unified chat page after authentication.
 *
 * @param {Request} request - The incoming request object.
 * @return {NextResponse} The redirect response to the unified chat page.
 */
const providerAuthRedirect = async (request: Request) => {
  const requestUrl = new URL(request.url);

  const roomId = requestUrl.searchParams.get("room");
  const token = requestUrl.searchParams.get("token");
  const email = requestUrl.searchParams.get("email");

  await setCookie(CONSTANT.ACCESS_TOKEN, token as string);

  // Build redirect URL with role parameter
  const redirectUrl = new URL(`/chat/${roomId}`, requestUrl.origin);
  redirectUrl.searchParams.set("role", CONSTANT.ROLES.PROVIDER);

  if (email) {
    redirectUrl.searchParams.set("email", email);
  }

  return redirect(redirectUrl.toString(), RedirectType.replace);
};

export { providerAuthRedirect as GET };
