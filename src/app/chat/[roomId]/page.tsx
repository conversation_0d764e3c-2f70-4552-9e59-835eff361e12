import { getCookie } from "@/actions/cookies";
import UnifiedChatPage from "@/components/chat/unified-chat-page";
import ErrorRendered from "@/components/shared/error/error-renderer";
import { apiServer } from "@/lib/apiServer";
import CONSTANT from "@/lib/constant";
import { UserRole } from "@/types/chat";
import React from "react";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

interface Props {
  params: {
    roomId: string;
  };
  searchParams: {
    role?: string;
    email?: string;
    token?: string;
  };
}

/**
 * Unified Chat Page
 * Handles all user roles (USER/PROVIDER/ADMIN) in a single route
 * Role is determined from URL params, token, or defaults to USER
 */
const ChatPage = async ({ params, searchParams }: Props) => {
  const { roomId } = params;
  const { role: urlRole, email, token: urlToken } = searchParams;

  // Determine user role with fallback to USER
  let userRole: UserRole = CONSTANT.DEFAULT_ROLE as UserRole;
  if (urlRole && Object.values(CONSTANT.ROLES).includes(urlRole as UserRole)) {
    userRole = urlRole as UserRole;
  }

  // Get token from URL params or cookies
  let token = urlToken || (await getCookie(CONSTANT.ACCESS_TOKEN));

  try {
    // For USER role, check if consultation exists
    if (userRole === CONSTANT.ROLES.USER) {
      const consultRes = await apiServer({
        method: "GET",
        endpoint: `room/${roomId}/exits-consult`,
        data: {},
        token: token || undefined,
      });

      if (consultRes?.statusCode >= 400) {
        return <ErrorRendered statusCode={consultRes.statusCode} />;
      }

      return (
        <UnifiedChatPage
          roomIdentifier={roomId}
          role={userRole}
          token={token}
          email={email}
          isConsult={consultRes?.data?.consultExists?.length > 0}
        />
      );
    }

    // For PROVIDER/ADMIN roles, get chat metadata
    const metadataRes = await apiServer({
      method: "GET",
      endpoint: `chat/${roomId}/metadata`,
      data: {},
      token: token || undefined,
    });

    if (metadataRes.statusCode >= 400) {
      return <ErrorRendered statusCode={metadataRes.statusCode} />;
    }

    const { patient, provider, room } = metadataRes.data;

    return (
      <UnifiedChatPage
        roomIdentifier={roomId}
        role={userRole}
        token={token}
        email={email}
        patient={patient}
        provider={provider}
        room={room}
      />
    );
  } catch (error) {
    console.error("Error loading chat page:", error);
    return <ErrorRendered statusCode={500} />;
  }
};

export default ChatPage;
