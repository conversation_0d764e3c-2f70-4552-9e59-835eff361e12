import { getCookie } from "@/actions/cookies";
import ChatViewPage from "@/components/chat/chat-view";
import ErrorRendered from "@/components/shared/error/error-renderer";
import { apiServer } from "@/lib/apiServer";
import CONSTANT from "@/lib/constant";
import React from "react";
export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

interface Props {
  params: {
    roomId: string;
  };
}

const ProviderChatPage = async ({ params }: Props) => {
  const roomId = params.roomId;
  const token = await getCookie(CONSTANT.ACCESS_TOKEN);
  const res = await apiServer({
    method: "GET",
    endpoint: `chat/${roomId}/metadata`,
    data: {},
    token: token || undefined,
  });
  if (res.statusCode >= 400) {
    return <ErrorRendered statusCode={res.statusCode} />;
  }
  const patient = res?.data?.patient;
  const provider = res?.data?.provider;
  const room = res?.data?.room;
  return (
    <>
      <ChatViewPage
        patient={patient}
        token={token ?? ""}
        provider={provider}
        room={room}
        role="PROVIDER"
        roomIdentifier={roomId}
      />
    </>
  );
};

export default ProviderChatPage;
