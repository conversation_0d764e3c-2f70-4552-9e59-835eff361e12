'use client';
import { createContext, useEffect, useState } from 'react';
import useWebSocket, {
  ReadyState,
  type SendMessage,
} from 'react-use-websocket';
import {
  SendJsonMessage,
  WebSocketLike,
} from 'react-use-websocket/dist/lib/types';

interface IWebSocketContext {
  sendMessage: SendMessage ;
  lastMessage: MessageEvent<any> | null;
  readyState: ReadyState | null;
  sendJsonMessage: SendJsonMessage ;
  getWebSocket: () => WebSocketLike | null;
  lastJsonMessage: unknown | null;
  connectionStatus: string;
  
}

const initWebSocketContext = {
  sendMessage: () => {},
  lastMessage: null,
  readyState: null,
  sendJsonMessage: () => {},
  getWebSocket: () => null,
  lastJsonMessage: null,
  connectionStatus: '',
};

interface IWebSocketProviderProps {
  children: React.ReactNode;
}

export const WebSocketContext =
  createContext<IWebSocketContext>(initWebSocketContext);

const SOCKET_URL = process.env.NEXT_PUBLIC_SOCKET_URL!;


const WebSocketProvider: React.FC<IWebSocketProviderProps> = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [socketUrl, setSocketUrl] = useState<string>(SOCKET_URL);

  useEffect(() => {
    setSocketUrl(SOCKET_URL);
  }, []);

  const {
    sendMessage,
    lastMessage,
    readyState,
    sendJsonMessage,
    getWebSocket,
    lastJsonMessage,    
  } = useWebSocket(socketUrl, {
    heartbeat: {
      message: 'ping',
      interval: 10000,
      returnMessage: 'pong',
      timeout: 60000,
    },
    shouldReconnect: () => true,
  });

  const connectionStatus = {
    [ReadyState.CONNECTING]: 'Connecting',
    [ReadyState.OPEN]: 'Open',
    [ReadyState.CLOSING]: 'Closing',
    [ReadyState.CLOSED]: 'Closed',
    [ReadyState.UNINSTANTIATED]: 'Uninstantiated',
  }[readyState];

  return (
    <WebSocketContext.Provider
      value={{
        sendMessage,
        lastMessage,
        readyState,
        sendJsonMessage,
        getWebSocket,
        lastJsonMessage,
        connectionStatus,
      }}
    >
      {children}
    </WebSocketContext.Provider>
  );
};
export default WebSocketProvider;
