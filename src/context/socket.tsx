"use client";
import React, { createContext, useEffect, useState } from "react";
import { io as Client<PERSON>, Socket } from "socket.io-client";

const socketUrl: string = process.env.NEXT_PUBLIC_BASE_URL!;
console.log("Connecting to socket:", socketUrl);

interface SocketContextType {
  socket: any | null;
  isConnected: boolean;
}

export const SocketContext = createContext<SocketContextType>({
  socket: null,
  isConnected: false,
});

const SocketProvider = ({ children }: { children: React.ReactNode }) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    const socketInstance = new (ClientIO as any)(socketUrl);
    console.log("Connecting to socket:", socketUrl);

    socketInstance.on("connect", () => {
      setIsConnected(true);
    });

    socketInstance.on("disconnect", () => {
      setIsConnected(false);
    });

    setSocket(socketInstance);

    return () => {
      socketInstance.disconnect();
    };
  }, []);

  return (
    <SocketContext.Provider value={{ socket, isConnected }}>
      {children}
    </SocketContext.Provider>
  );
};

export default SocketProvider;
