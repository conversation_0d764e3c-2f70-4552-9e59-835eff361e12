# Unified Chat Architecture

## Overview

This document describes the unified chat architecture that consolidates all user roles (USER/PROVIDER/ADMIN) into a single, streamlined system.

## Key Features

- **Unified URL Structure**: Single `/chat/[roomId]` route for all user types
- **Role-Based Access**: Automatic role detection and appropriate UI rendering
- **Inline Authentication**: OTP-based authentication flow for users without tokens
- **Cookie-Based Security**: Secure token storage using HTTP-only cookies
- **Consolidated Components**: Single chat component handling all user roles

## URL Structure

### New Unified Route
```
/chat/[roomId]?role=USER&email=<EMAIL>
/chat/[roomId]?role=PROVIDER&token=jwt_token
/chat/[roomId]?role=ADMIN&email=<EMAIL>
```

### Old Routes (Removed)
- ~~`/chat/patient/[roomId]`~~
- ~~`/chat/provider/[roomId]`~~
- ~~`/chat-provider`~~

## Role System

### Supported Roles
- **USER** (default): Patient/end-user access
- **PROVIDER**: Healthcare provider access
- **ADMIN**: Administrative access with full permissions

### Role Detection Priority
1. URL parameter (`?role=PROVIDER`)
2. JWT token role claim
3. Stored cookie role
4. Default to USER

### Role Features
```typescript
// USER features
- Basic chat functionality
- OTP authentication
- Consultation status viewing

// PROVIDER features
- All USER features
- Consultation toggle controls
- Provider-specific UI elements

// ADMIN features
- All PROVIDER features
- User management capabilities
- Admin panel access
- Room management
```

## Authentication Flow

### For Users (USER role)
1. Access `/chat/[roomId]?role=USER&email=<EMAIL>`
2. If no token, show inline OTP modal
3. Auto-fill email from URL parameter
4. Send OTP to email
5. Verify OTP and store token in cookies
6. Load chat interface

### For Providers/Admins
1. Access `/chat/[roomId]?role=PROVIDER&token=jwt_token`
2. Validate token and extract role
3. Store token in cookies
4. Load chat interface with role-specific features

## Component Architecture

### Main Components
- `UnifiedChatPage`: Handles authentication and initialization
- `UnifiedChatComponent`: Core chat interface for all roles
- `RoleUtils`: Utility functions for role management
- `TokenManager`: Secure token and role storage

### Removed Components
- ~~`UserChatUIComponent`~~
- ~~`ProviderChatUIComponent`~~
- ~~`ChatViewPage`~~
- ~~`PatientChatComponent`~~

## API Integration

### Role-Based Data Fetching
```typescript
// For USER role
GET /room/{roomId}/exits-consult

// For PROVIDER/ADMIN roles
GET /chat/{roomId}/metadata
```

### Authentication Endpoints
- `POST /auth/send-otp`: Send OTP to email
- `POST /auth/verify-otp`: Verify OTP and get token

## Security Improvements

### Cookie-Based Storage
- Access tokens stored in HTTP-only cookies
- Role information stored securely
- Automatic migration from localStorage

### Token Management
- Centralized token operations
- Automatic cleanup of legacy tokens
- Role extraction from JWT claims

## Migration Guide

### For Existing Links
Old provider links will automatically redirect:
```
/chat/provider/123 → /chat/123?role=PROVIDER
```

Old patient links will automatically redirect:
```
/chat/patient/123 → /chat/123?role=USER
```

### For API Clients
Update redirect URLs to use the new unified structure:
```typescript
// Old
redirect(`/chat/provider/${roomId}`)

// New
redirect(`/chat/${roomId}?role=PROVIDER`)
```

## Testing

### Manual Testing Scenarios
1. **User Authentication**: Test OTP flow with email auto-fill
2. **Provider Access**: Test direct token-based access
3. **Admin Features**: Verify admin-specific UI elements
4. **Role Switching**: Test role parameter changes
5. **Legacy Redirects**: Verify old URLs redirect properly

### Automated Tests
- Role utility functions
- Token management
- Authentication flows
- Component rendering

## Benefits

1. **Simplified Maintenance**: Single codebase for all user types
2. **Consistent UX**: Unified authentication and chat experience
3. **Better Security**: Cookie-based token storage
4. **Easier Testing**: Consolidated test scenarios
5. **Cleaner URLs**: Semantic role-based parameters

## Future Enhancements

- Role-based feature toggles
- Dynamic permission system
- Multi-tenant support
- Advanced admin controls
